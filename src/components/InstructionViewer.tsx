"use client";

import type { Revision } from "@/app/api/recognize-instructions/route";
import React, { useState, useEffect } from "react";
import { X, MoveDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardAction,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface ResultViewerProps {
  analysis: Revision | null;
  onAnalysisUpdate?: (updatedAnalysis: Revision) => void;
}

export default function InstructionViewer({
  analysis,
  onAnalysisUpdate,
}: ResultViewerProps) {
  const [instructions, setInstructions] = useState<Revision["instructions"]>(
    analysis?.instructions || []
  );

  // Update local state when analysis changes
  useEffect(() => {
    setInstructions(analysis?.instructions || []);
  }, [analysis]);

  if (!analysis) return null;

  const handleDelete = (index: number) => {
    const newScripts = instructions.filter((_, i) => i !== index);
    setInstructions(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ instructions: newScripts });
    }
  };

  const handleLinesChange = (index: number, newLines: string) => {
    // const newScripts = [...instructions];
    // newScripts[index] = { ...newScripts[index], content: newLines };
    // setInstructions(newScripts);
    // // Notify parent component of the change
    // if (onAnalysisUpdate) {
    //   onAnalysisUpdate({ instructions: newScripts });
    // }
  };

  const handleTypeChange = (
    index: number,
    newType: "insert" | "delete" | "replace"
  ) => {
    // const newScripts = [...instructions];
    // newScripts[index] = { ...newScripts[index], type: newType };
    // setInstructions(newScripts);
    // // Notify parent component of the change
    // if (onAnalysisUpdate) {
    //   onAnalysisUpdate({ instructions: newScripts });
    // }
  };

  const getTypeStyles = (type: string) => {
    switch (type) {
      case "insert":
        return {
          cardClass:
            "border-blue-300/70 bg-gradient-to-br from-blue-50 via-blue-25 to-white",
          headerClass:
            "bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",
        };
      case "delete":
        return {
          cardClass:
            "border-purple-300/70 bg-gradient-to-br from-purple-50 via-purple-25 to-white",
          headerClass:
            "bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent",
        };
      case "replace":
        return {
          cardClass:
            "border-green-300/70 bg-gradient-to-br from-green-50 via-green-25 to-white",
          headerClass:
            "bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",
        };
      default:
        return {
          cardClass:
            "border-gray-300/70 bg-gradient-to-br from-gray-50 via-gray-25 to-white",
          headerClass:
            "bg-gradient-to-r from-gray-600 to-gray-700 bg-clip-text text-transparent",
        };
    }
  };

  return (
    <div className="flex flex-col gap-6 max-w-md w-full mx-auto">
      <h2 className="text-xl font-bold">分析結果</h2>
      <div className="flex flex-col space-y-6">
        {instructions.map((instruction, index) => {
          const typeStyles = getTypeStyles(instruction.type);
          return (
            <Card key={index} className={`${typeStyles.cardClass}`}>
              <CardHeader>
                <CardTitle className={`${typeStyles.headerClass}`}>
                  <Select
                    value={instruction.type}
                    onValueChange={(value) =>
                      handleTypeChange(
                        index,
                        value as "insert" | "delete" | "replace"
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="修正タイプを選択" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>修正タイプ</SelectLabel>
                        <SelectItem value="insert">挿入</SelectItem>
                        <SelectItem value="delete">削除</SelectItem>
                        <SelectItem value="replace">置換</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </CardTitle>
                <CardAction>
                  {/* Delete button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(index)}
                  >
                    <X className="w-4 h-4 text-red-500 hover:text-red-600" />
                  </Button>
                </CardAction>
              </CardHeader>
              <CardContent>
                <h4 className="text-sm font-bold my-2">適用箇所</h4>
                <Textarea
                  value={instruction.position}
                  onChange={(e) => handleLinesChange(index, e.target.value)}
                />
                {instruction.type === "insert" ? (
                  <>
                    <h4 className="text-sm font-bold my-2">挿入テキスト</h4>
                    <Textarea
                      value={instruction.text}
                      onChange={(e) => handleLinesChange(index, e.target.value)}
                    />
                  </>
                ) : instruction.type === "delete" ? (
                  <>
                    <h4 className="text-sm font-bold my-2">削除テキスト</h4>
                    <Textarea
                      value={instruction.text}
                      onChange={(e) => handleLinesChange(index, e.target.value)}
                    />
                  </>
                ) : (
                  <>
                    <h4 className="text-sm font-bold my-2">変更前テキスト</h4>
                    <Textarea
                      value={instruction.before}
                      onChange={(e) => handleLinesChange(index, e.target.value)}
                    />
                    <MoveDown className="w-4 h-4 text-gray-500 p-2" />
                    <h4 className="text-sm font-bold my-2">変更後テキスト</h4>
                    <Textarea
                      value={instruction.after}
                      onChange={(e) => handleLinesChange(index, e.target.value)}
                    />
                  </>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
