# 雑誌の原稿画像からの修正指示抽出タスク

## 目的

雑誌の原稿画像から修正指示を抽出し、構造化された JSON データに変換する。

## 入力情報

- 画像には、挿入、削除、置換の 3 種類の修正指示が含まれている。
- **挿入(insert)**: 元文章の途中に赤い線が入り、文章が追記されている。追加の文(text)を挿入する。
- **削除(delete)**: 元文章の上に赤い線が引かれ、「トル」と書かれている。元の文(text)を消す。
- **置換(replace)**: 元文章の上に赤い線が引かれ、代替の文章が提示されている。元の文(before)を代替の文(after)に書き換える。

## 処理手順

1. 画像内のすべてのテキストを認識
2. 修正指示の適用箇所(position)を特定
3. 修正指示の種類(type)を分類
4. JSON フォーマットで結果を出力

## 注意事項

- 文章は省略せず、すべての文字を正確に認識して出力すること。
- 文法を考慮した上で、自然な文章になっているかを確認すること。
