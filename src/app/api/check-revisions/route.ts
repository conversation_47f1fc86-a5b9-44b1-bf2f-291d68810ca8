import { NextResponse } from "next/server";
import OpenAI from "openai";
import { zodTextFormat } from "openai/helpers/zod";
import { z } from "zod";
import { GoogleGenAI, Type } from "@google/genai";
import Anthropic from "@anthropic-ai/sdk";
import fs from "fs/promises";
import path from "path";

const instructionSchema = z.object({
  type: z.enum(["insert", "delete", "replace"]),
  position: z.string(),
});

const insertSchema = instructionSchema.extend({
  type: z.literal("insert"),
  text: z.string(),
});

const deleteSchema = instructionSchema.extend({
  type: z.literal("delete"),
  text: z.string(),
});

const replaceSchema = instructionSchema.extend({
  type: z.literal("replace"),
  before: z.string(),
  after: z.string(),
});

const revisionSchema = z.object({
  instructions: z.array(insertSchema.or(deleteSchema).or(replaceSchema)),
});

export type Revision = z.infer<typeof revisionSchema>;

const gemini = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const model = formData.get("model") as string;
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return NextResponse.json(
        { error: "Image data is required" },
        { status: 400 }
      );
    }

    // Extract base64 data from data URL
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, "");
    const targetImage = Buffer.from(base64Data, "base64").buffer;

    const instruction =
      "優秀な画像認識AIとして、与えられた画像から修正指示を抽出し、構造化されたJSONデータに変換してください。";
    const prompt = await fs.readFile(
      path.join(process.cwd(), "src/app/api/recognize/prompt.md"),
      "utf-8"
    );

    let analysis: Revision;

    switch (model.split("-")[0]) {
      case "gemini": {
        const response = await gemini.models.generateContent({
          model: model,
          contents: [
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(targetImage).toString("base64"),
              },
            },
            { text: prompt },
          ],
          config: {
            temperature: 0.2,
            systemInstruction: instruction,
            responseMimeType: "application/json",
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                instructions: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      type: {
                        type: Type.STRING,
                        description:
                          'Instruction type ("insert" | "delete" | "replace")',
                        nullable: false,
                      },
                      position: {
                        type: Type.STRING,
                        description: "Position",
                        nullable: false,
                      },
                      text: {
                        type: Type.STRING,
                        description: "Text",
                        nullable: false,
                      },
                    },
                    required: ["type", "position", "text"],
                    propertyOrdering: ["type", "position", "text"],
                  },
                },
              },
              required: ["instructions"],
              propertyOrdering: ["instructions"],
            },
          },
        });
        analysis = JSON.parse(response.text ?? "{}") as Revision;
        break;
      }

      case "claude": {
        const response = await anthropic.messages.create({
          model: model,
          max_tokens: 8192,
          temperature: 0.2,
          system: instruction,
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/jpeg" as const,
                    data: Buffer.from(targetImage).toString("base64"),
                  },
                },
                {
                  type: "text",
                  text: prompt,
                },
              ],
            },
          ],
        });
        analysis = JSON.parse(
          response.content[0].type === "text" ? response.content[0].text : "{}"
        ) as Revision;
        break;
      }

      default: {
        // OpenAI
        const response = await openai.responses.parse({
          model: model,
          temperature: model.startsWith("gpt") ? 0.2 : null,
          instructions: instruction,
          input: [
            {
              role: "user",
              content: [
                { type: "input_text", text: prompt },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    targetImage
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
              ],
            },
          ],
          text: {
            format: zodTextFormat(revisionSchema, "analysis"),
          },
        });
        analysis = response.output_parsed ?? ({} as Revision);
        break;
      }
    }

    return NextResponse.json({ analysis });
  } catch (error) {
    console.error("Error processing files:", error);
    return NextResponse.json(
      { error: "Failed to process files" },
      { status: 500 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};
