"use client";

import ModelSelector from "@/components/ModelSelector";
import ProofreadingForm from "@/components/ProofreadingForm";
import MessageDisplay from "@/components/MessageDisplay";
import InstructionViewer from "@/components/InstructionViewer";
import { useState } from "react";
import { Revision } from "@/app/api/recognize-instructions/route";
import { MODELS } from "@/lib/const";

export default function Home() {
  const [filesBefore, setFilesBefore] = useState<FileList | null>(null);
  const [filesAfter, setFilesAfter] = useState<FileList | null>(null);
  const [imagesBefore, setImagesBefore] = useState<string[]>([]);
  const [imagesAfter, setImagesAfter] = useState<string[]>([]);
  const [currentPageBefore, setCurrentPageBefore] = useState(0);
  const [currentPageAfter, setCurrentPageAfter] = useState(0);

  const [analysis, setAnalysis] = useState<Revision[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [model, setModel] = useState(MODELS.OpenAI[0].id);
  const [isLoadingImages, setIsLoadingImages] = useState(false);

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "before" | "after"
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const firstFile = e.target.files[0];
      console.log("handleFileChange called with type:", type);
      let setFiles: React.Dispatch<React.SetStateAction<FileList | null>>;
      let setImages: React.Dispatch<React.SetStateAction<string[]>>;
      let setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
      if (type === "before") {
        console.log("Processing BEFORE file:", firstFile.name);
        setFiles = setFilesBefore;
        setImages = setImagesBefore;
        setCurrentPage = setCurrentPageBefore;
      } else {
        console.log("Processing AFTER file:", firstFile.name);
        setFiles = setFilesAfter;
        setImages = setImagesAfter;
        setCurrentPage = setCurrentPageAfter;
      }

      // Automatically detect file type
      if (firstFile.type === "application/pdf") {
        // Handle PDF upload (single file)
        if (e.target.files.length > 1) {
          setError("PDFファイルは1つのみ選択してください");
          setFiles(null);
          setImages([]);
          setAnalysis([]);
          setCurrentPage(0);
          return;
        }

        setFiles(e.target.files);
        // Convert PDF to images
        setIsLoadingImages(true);
        try {
          const formData = new FormData();
          formData.append("file", firstFile);

          const response = await fetch("/api/pdf-to-images", {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            throw new Error("PDF画像変換に失敗しました");
          }

          const data = await response.json();
          setImages(data.images);
        } catch (err) {
          setError(
            err instanceof Error ? err.message : "PDF変換エラーが発生しました"
          );
          setImages([]);
        } finally {
          setIsLoadingImages(false);
        }
        setCurrentPage(0);
      } else if (
        ["image/jpeg", "image/png", "image/webp"].includes(firstFile.type)
      ) {
        // Handle image upload (multiple files allowed)
        const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
        const areValidImages = Array.from(e.target.files).every((file) =>
          allowedTypes.includes(file.type)
        );

        if (areValidImages) {
          setFiles(e.target.files);
          setError(null);
          const imageUrls = Array.from(e.target.files).map((file) =>
            URL.createObjectURL(file)
          );
          setImages(imageUrls);
          setCurrentPage(0);
        } else {
          setError(
            "対応する画像ファイル（JPEG、PNG、WEBP）のみを選択してください"
          );
          setFiles(null);
          setImages([]);
          setAnalysis([]);
          setCurrentPage(0);
        }
      } else {
        setError(
          "PDFファイルまたは画像ファイル（JPEG、PNG、WEBP）を選択してください"
        );
        setFiles(null);
        setImages([]);
        setAnalysis([]);
        setCurrentPage(0);
      }
    }
  };

  const rotateBase64Image = (base64Data: string): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        // Set canvas dimensions (swap width/height for 90-degree rotation)
        canvas.width = img.height;
        canvas.height = img.width;

        // Move to center, rotate, then move back
        ctx!.translate(canvas.width / 2, canvas.height / 2);
        ctx!.rotate(Math.PI / 2); // 90 degrees in radians
        ctx!.drawImage(img, -img.width / 2, -img.height / 2);

        // Convert back to base64
        const rotatedBase64 = canvas.toDataURL("image/jpeg", 0.9);
        resolve(rotatedBase64);
      };

      img.src = base64Data;
    });
  };

  const handleRotateImage = async (
    type: "before" | "after",
    pageIndex: number
  ) => {
    const images = type === "before" ? imagesBefore : imagesAfter;
    const setImages = type === "before" ? setImagesBefore : setImagesAfter;

    try {
      const rotatedImage = await rotateBase64Image(images[pageIndex]);

      // Update the image data
      setImages((prev) => {
        const newImages = [...prev];
        newImages[pageIndex] = rotatedImage;
        return newImages;
      });
    } catch (error) {
      console.error("Failed to rotate image:", error);
      setError("画像の回転に失敗しました");
    }
  };

  const handleSubmitBefore = async (e: React.FormEvent) => {
    e.preventDefault();
    if (imagesBefore.length === 0) return;
    console.log("submit");

    setIsAnalyzing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", imagesBefore[currentPageBefore]);

      const response = await fetch("/api/recognize-instructions", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("ファイルの解析に失敗しました");
      }

      const data = await response.json();
      setAnalysis((prev) => {
        prev[currentPageBefore] = data.analysis;
        return prev;
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSubmitAfter = async (e: React.FormEvent) => {
    e.preventDefault();
    if (imagesAfter.length === 0) return;
    console.log("submit after");

    setIsAnalyzing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", imagesAfter[currentPageAfter]);
      formData.append(
        "instructions",
        JSON.stringify(analysis[currentPageAfter])
      );

      const response = await fetch("/api/check-revisions", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("ファイルの解析に失敗しました");
      }

      const data = await response.json();
      setAnalysis((prev) => {
        prev[currentPageAfter] = data.analysis;
        return prev;
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12 flex flex-col items-center justify-center">
      <ModelSelector model={model} setModel={setModel} />
      <MessageDisplay error={error} />
      <div className="flex justify-center gap-6 w-full">
        <ProofreadingForm
          title="校正前ファイル"
          files={filesBefore}
          images={imagesBefore}
          currentPage={currentPageBefore}
          isAnalyzing={isAnalyzing}
          isLoadingImages={isLoadingImages}
          onFileChange={(e) => handleFileChange(e, "before")}
          onSubmit={handleSubmitBefore}
          onPageChange={setCurrentPageBefore}
          onRotateImage={(pageIndex) => handleRotateImage("before", pageIndex)}
        />
        <ProofreadingForm
          title="校正後ファイル"
          files={filesAfter}
          images={imagesAfter}
          currentPage={currentPageAfter}
          isAnalyzing={isAnalyzing}
          isLoadingImages={isLoadingImages}
          onFileChange={(e) => handleFileChange(e, "after")}
          onSubmit={handleSubmitAfter}
          onPageChange={setCurrentPageAfter}
          onRotateImage={(pageIndex) => handleRotateImage("after", pageIndex)}
        />
      </div>
      {analysis[currentPageBefore] && (
        <InstructionViewer
          analysis={analysis[currentPageBefore]}
          onAnalysisUpdate={(updatedAnalysis) => {
            setAnalysis((prev) => {
              prev[currentPageBefore] = updatedAnalysis;
              return prev;
            });
          }}
        />
      )}
    </div>
  );
}
